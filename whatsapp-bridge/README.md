# WhatsApp Bridge with Chat Frontend

A simple WhatsApp bridge with REST API and web-based chat interface.

## Features

- **Multiple Device Support**: Add and manage multiple WhatsApp devices
- **REST API**: Complete API for WhatsApp operations
- **Web Chat Interface**: Simple, clean chat interface
- **Message History**: Store and retrieve message history
- **Media Support**: Send and receive images, videos, audio, and documents

## Quick Start

1. **Build and Run**:
   ```bash
   cd whatsapp-bridge
   go run main.go
   ```

2. **Scan QR Code**: When first running, scan the QR code with your WhatsApp app

3. **Access Web Interface**: Open http://localhost:8080 in your browser

## API Endpoints

### Device Management
- `GET /api/devices` - List all devices
- `POST /api/devices` - Add a new device

### Chat Operations
- `GET /api/chats` - List all chats
- `GET /api/chat?chat_jid=<jid>` - Get specific chat info
- `GET /api/messages?chat_jid=<jid>` - Get messages for a chat

### Contact Operations
- `GET /api/contacts/search?query=<query>` - Search contacts

### Messaging
- `POST /api/send` - Send a message
  ```json
  {
    "recipient": "<EMAIL>",
    "message": "Hello World"
  }
  ```

### Media
- `POST /api/download` - Download media from a message
  ```json
  {
    "message_id": "message_id",
    "chat_jid": "chat_jid"
  }
  ```

## Web Interface

The web interface provides:
- **Chat List**: View all your chats in the sidebar
- **Message View**: Click on a chat to view messages
- **Send Messages**: Type and send messages directly
- **Search**: Search through your chats
- **Real-time Updates**: Refresh to see new messages

## File Structure

```
whatsapp-bridge/
├── main.go              # Main Go server
├── frontend/            # Web interface
│   ├── index.html      # Main HTML file
│   └── static/
│       ├── style.css   # Styling
│       └── app.js      # JavaScript functionality
├── store/              # Database files (auto-created)
└── README.md           # This file
```

## Configuration

- **Port**: Server runs on port 8080 by default
- **Database**: SQLite databases stored in `store/` directory
- **Frontend**: Static files served from `frontend/` directory

## Device Management

You can add multiple WhatsApp devices:

1. **Add Device**: `POST /api/devices` with `{"device_id": "device2"}`
2. **Use Device**: Add `?device_id=device2` to API calls
3. **List Devices**: `GET /api/devices`

Each device has its own:
- WhatsApp session
- Message database
- QR code for pairing

## Development

The implementation is kept simple as requested:
- Single Go file for the server
- Basic HTML/CSS/JS for frontend
- SQLite for data storage
- No external dependencies beyond Go modules

## Notes

- First run requires QR code scanning
- Messages are stored locally in SQLite
- Media files are downloaded to `store/` directory
- CORS is enabled for frontend development
