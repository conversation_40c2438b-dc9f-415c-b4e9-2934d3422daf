# WhatsApp Bridge with Chat Frontend

A simple WhatsApp bridge with REST API and web-based chat interface featuring device management with QR code pairing.

## Features

- **Multiple Device Support**: Add and manage multiple WhatsApp devices through the web interface
- **QR Code Pairing**: Scan QR codes directly in the browser to pair new devices
- **REST API**: Complete API for WhatsApp operations
- **Web Chat Interface**: Simple, clean chat interface with device switching
- **Message History**: Store and retrieve message history per device
- **Media Support**: Send and receive images, videos, audio, and documents

## Quick Start

1. **Build and Run**:

   ```bash
   cd whatsapp-bridge
   go run main.go
   ```

2. **Access Web Interface**: Open http://localhost:8080 in your browser

3. **Add Device**: Click "Add Device" to create a new WhatsApp connection

4. **Scan QR Code**: Scan the QR code displayed in the browser with your WhatsApp app

5. **Select Device**: Click on a connected device to view its chats and send messages

## API Endpoints

### Device Management

- `GET /api/devices` - List all devices with connection status
- `POST /api/devices` - Add a new device
  ```json
  {
    "device_id": "my-phone"
  }
  ```
- `POST /api/devices/qr` - Generate QR code for device pairing
  ```json
  {
    "device_id": "my-phone"
  }
  ```
- `GET /api/devices/status?device_id=<id>` - Check device connection status
- `DELETE /api/devices` - Remove a device
  ```json
  {
    "device_id": "my-phone"
  }
  ```

### Chat Operations

- `GET /api/chats` - List all chats
- `GET /api/chat?chat_jid=<jid>` - Get specific chat info
- `GET /api/messages?chat_jid=<jid>` - Get messages for a chat

### Contact Operations

- `GET /api/contacts/search?query=<query>` - Search contacts

### Messaging

- `POST /api/send` - Send a message
  ```json
  {
    "recipient": "<EMAIL>",
    "message": "Hello World"
  }
  ```

### Media

- `POST /api/download` - Download media from a message
  ```json
  {
    "message_id": "message_id",
    "chat_jid": "chat_jid"
  }
  ```

## Web Interface

The web interface provides:

- **Device Management**: Add, connect, and switch between multiple WhatsApp devices
- **QR Code Display**: Scan QR codes directly in the browser for device pairing
- **Device Status**: See which devices are connected and ready to use
- **Chat List**: View all your chats for the selected device
- **Message View**: Click on a chat to view messages
- **Send Messages**: Type and send messages directly
- **Search**: Search through your chats
- **Real-time Updates**: Refresh to see new messages

### Using the Interface

1. **Add a Device**: Click "Add Device" and enter a name (e.g., "Phone1", "Tablet")
2. **Pair Device**: Click "Connect" on the new device to show QR code
3. **Scan QR Code**: Use WhatsApp app to scan the displayed QR code (just like WhatsApp Web)
4. **Wait for Connection**: The device will show "Connected" once pairing is successful
5. **Select Device**: Click "Select" on a connected device to view its chats
6. **Chat**: Click on any chat to view messages and send new ones
7. **Remove Device**: Click "Remove" to delete a device (except the default one)

## File Structure

```
whatsapp-bridge/
├── main.go              # Main Go server
├── frontend/            # Web interface
│   ├── index.html      # Main HTML file
│   └── static/
│       ├── style.css   # Styling
│       └── app.js      # JavaScript functionality
├── store/              # Database files (auto-created)
└── README.md           # This file
```

## Configuration

- **Port**: Server runs on port 8080 by default
- **Database**: SQLite databases stored in `store/` directory
- **Frontend**: Static files served from `frontend/` directory

## Device Management

You can add multiple WhatsApp devices:

1. **Add Device**: `POST /api/devices` with `{"device_id": "device2"}`
2. **Use Device**: Add `?device_id=device2` to API calls
3. **List Devices**: `GET /api/devices`

Each device has its own:

- WhatsApp session
- Message database
- QR code for pairing

## Development

The implementation is kept simple as requested:

- Single Go file for the server
- Basic HTML/CSS/JS for frontend
- SQLite for data storage
- No external dependencies beyond Go modules

## Notes

- First run requires QR code scanning
- Messages are stored locally in SQLite
- Media files are downloaded to `store/` directory
- CORS is enabled for frontend development
