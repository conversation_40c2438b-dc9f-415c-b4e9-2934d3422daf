#!/bin/bash

# Test script for WhatsApp Bridge API
BASE_URL="http://localhost:8080/api"

echo "Testing WhatsApp Bridge API..."
echo "================================"

# Test device listing
echo "1. Testing device listing..."
curl -s "$BASE_URL/devices" | jq '.' || echo "Failed to get devices"
echo ""

# Test chat listing
echo "2. Testing chat listing..."
curl -s "$BASE_URL/chats" | jq '.' || echo "Failed to get chats"
echo ""

# Test contact search
echo "3. Testing contact search..."
curl -s "$BASE_URL/contacts/search?query=test" | jq '.' || echo "Failed to search contacts"
echo ""

# Test message listing
echo "4. Testing message listing..."
curl -s "$BASE_URL/messages?limit=5" | jq '.' || echo "Failed to get messages"
echo ""

echo "API tests completed!"
echo ""
echo "To test the web interface, open: http://localhost:8080"
