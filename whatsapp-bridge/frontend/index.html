<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>WhatsApp Bridge</title>
    <link rel="stylesheet" href="/static/style.css" />
    <script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"></script>
  </head>
  <body>
    <div class="container">
      <div class="sidebar">
        <div class="sidebar-header">
          <h2>WhatsApp Bridge</h2>
          <button id="add-device" class="btn btn-small">Add Device</button>
        </div>

        <!-- Device Management Section -->
        <div class="devices-section">
          <h3>Devices</h3>
          <div id="device-list" class="device-list">
            <!-- Devices will be loaded here -->
          </div>
        </div>

        <!-- Chat Section -->
        <div class="chats-section" style="display: none">
          <div class="section-header">
            <h3>Chats</h3>
            <button id="refresh-chats" class="btn btn-small">Refresh</button>
          </div>
          <div class="search-container">
            <input type="text" id="chat-search" placeholder="Search chats..." />
          </div>
          <div id="chat-list" class="chat-list">
            <!-- Chats will be loaded here -->
          </div>
        </div>
      </div>

      <div class="main-content">
        <div class="chat-header" id="chat-header" style="display: none">
          <h3 id="chat-title">Select a chat</h3>
          <div class="chat-info">
            <span id="chat-jid"></span>
          </div>
        </div>

        <div class="messages-container" id="messages-container">
          <div class="no-chat-selected">
            <h3>Welcome to WhatsApp Bridge</h3>
            <p>
              Add a device to get started, then select a chat to view messages
            </p>
          </div>
        </div>

        <div
          class="message-input-container"
          id="message-input-container"
          style="display: none">
          <div class="message-input">
            <input
              type="text"
              id="message-text"
              placeholder="Type a message..." />
            <button id="send-message" class="btn btn-primary">Send</button>
          </div>
        </div>
      </div>
    </div>

    <div id="loading" class="loading" style="display: none">
      <div class="spinner"></div>
      <p>Loading...</p>
    </div>

    <!-- Add Device Modal -->
    <div id="add-device-modal" class="modal" style="display: none">
      <div class="modal-content">
        <div class="modal-header">
          <h3>Add New Device</h3>
          <span class="close" id="close-add-device">&times;</span>
        </div>
        <div class="modal-body">
          <div class="form-group">
            <label for="device-name">Device Name:</label>
            <input
              type="text"
              id="device-name"
              placeholder="Enter device name (e.g., Phone1, Tablet)" />
          </div>
          <div class="modal-actions">
            <button id="create-device" class="btn btn-primary">
              Create Device
            </button>
            <button id="cancel-add-device" class="btn">Cancel</button>
          </div>
        </div>
      </div>
    </div>

    <!-- QR Code Modal -->
    <div id="qr-modal" class="modal" style="display: none">
      <div class="modal-content">
        <div class="modal-header">
          <h3>Scan QR Code</h3>
          <span class="close" id="close-qr">&times;</span>
        </div>
        <div class="modal-body">
          <div class="qr-container">
            <div id="qr-code-display"></div>
            <p>Scan this QR code with your WhatsApp app</p>
            <div class="qr-status">
              <span id="qr-status">Generating QR code...</span>
            </div>
          </div>
          <div class="modal-actions">
            <button id="close-qr-modal" class="btn">Close</button>
          </div>
        </div>
      </div>
    </div>

    <script src="/static/app.js"></script>
  </body>
</html>
