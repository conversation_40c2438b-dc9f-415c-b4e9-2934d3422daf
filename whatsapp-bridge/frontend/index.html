<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WhatsApp Chat Interface</title>
    <link rel="stylesheet" href="/static/style.css">
</head>
<body>
    <div class="container">
        <div class="sidebar">
            <div class="sidebar-header">
                <h2>WhatsApp Chats</h2>
                <button id="refresh-chats" class="btn btn-small">Refresh</button>
            </div>
            <div class="search-container">
                <input type="text" id="chat-search" placeholder="Search chats..." />
            </div>
            <div id="chat-list" class="chat-list">
                <!-- Chats will be loaded here -->
            </div>
        </div>
        
        <div class="main-content">
            <div class="chat-header" id="chat-header" style="display: none;">
                <h3 id="chat-title">Select a chat</h3>
                <div class="chat-info">
                    <span id="chat-jid"></span>
                </div>
            </div>
            
            <div class="messages-container" id="messages-container">
                <div class="no-chat-selected">
                    <h3>Welcome to WhatsApp Chat Interface</h3>
                    <p>Select a chat from the sidebar to start viewing messages</p>
                </div>
            </div>
            
            <div class="message-input-container" id="message-input-container" style="display: none;">
                <div class="message-input">
                    <input type="text" id="message-text" placeholder="Type a message..." />
                    <button id="send-message" class="btn btn-primary">Send</button>
                </div>
            </div>
        </div>
    </div>

    <div id="loading" class="loading" style="display: none;">
        <div class="spinner"></div>
        <p>Loading...</p>
    </div>

    <script src="/static/app.js"></script>
</body>
</html>
