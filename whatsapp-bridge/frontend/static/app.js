class WhatsAppBridge {
  constructor() {
    this.apiBase = "http://localhost:8080/api";
    this.currentChatJID = null;
    this.currentDeviceID = null;
    this.devices = [];
    this.chats = [];
    this.messages = [];

    this.initializeElements();
    this.bindEvents();
    this.loadDevices();
  }

  initializeElements() {
    // Device management elements
    this.deviceList = document.getElementById("device-list");
    this.addDeviceButton = document.getElementById("add-device");
    this.addDeviceModal = document.getElementById("add-device-modal");
    this.deviceNameInput = document.getElementById("device-name");
    this.createDeviceButton = document.getElementById("create-device");
    this.cancelAddDeviceButton = document.getElementById("cancel-add-device");
    this.closeAddDeviceButton = document.getElementById("close-add-device");

    // QR Code modal elements
    this.qrModal = document.getElementById("qr-modal");
    this.qrCodeDisplay = document.getElementById("qr-code-display");
    this.qrStatus = document.getElementById("qr-status");
    this.closeQrButton = document.getElementById("close-qr");
    this.closeQrModalButton = document.getElementById("close-qr-modal");

    // Chat elements
    this.chatsSection = document.querySelector(".chats-section");
    this.chatList = document.getElementById("chat-list");
    this.messagesContainer = document.getElementById("messages-container");
    this.chatHeader = document.getElementById("chat-header");
    this.chatTitle = document.getElementById("chat-title");
    this.chatJID = document.getElementById("chat-jid");
    this.messageInputContainer = document.getElementById(
      "message-input-container"
    );
    this.messageText = document.getElementById("message-text");
    this.sendButton = document.getElementById("send-message");
    this.refreshButton = document.getElementById("refresh-chats");
    this.chatSearch = document.getElementById("chat-search");
    this.loading = document.getElementById("loading");
  }

  bindEvents() {
    // Device management events
    this.addDeviceButton.addEventListener("click", () =>
      this.showAddDeviceModal()
    );
    this.createDeviceButton.addEventListener("click", () =>
      this.createDevice()
    );
    this.cancelAddDeviceButton.addEventListener("click", () =>
      this.hideAddDeviceModal()
    );
    this.closeAddDeviceButton.addEventListener("click", () =>
      this.hideAddDeviceModal()
    );

    // QR modal events
    this.closeQrButton.addEventListener("click", () => this.hideQrModal());
    this.closeQrModalButton.addEventListener("click", () => this.hideQrModal());

    // Chat events
    this.sendButton.addEventListener("click", () => this.sendMessage());
    this.messageText.addEventListener("keypress", (e) => {
      if (e.key === "Enter") {
        this.sendMessage();
      }
    });
    this.refreshButton.addEventListener("click", () => this.loadChats());
    this.chatSearch.addEventListener("input", (e) =>
      this.filterChats(e.target.value)
    );

    // Modal backdrop clicks
    this.addDeviceModal.addEventListener("click", (e) => {
      if (e.target === this.addDeviceModal) {
        this.hideAddDeviceModal();
      }
    });

    this.qrModal.addEventListener("click", (e) => {
      if (e.target === this.qrModal) {
        this.hideQrModal();
      }
    });
  }

  showLoading() {
    this.loading.style.display = "flex";
  }

  hideLoading() {
    this.loading.style.display = "none";
  }

  async apiCall(endpoint, options = {}) {
    try {
      const response = await fetch(`${this.apiBase}${endpoint}`, {
        headers: {
          "Content-Type": "application/json",
          ...options.headers,
        },
        ...options,
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error("API call failed:", error);
      this.showError(`API call failed: ${error.message}`);
      return null;
    }
  }

  // Device Management Methods
  async loadDevices() {
    this.showLoading();
    try {
      const response = await this.apiCall("/devices");
      if (response && response.success) {
        this.devices = response.data || [];
        this.renderDevices();
      } else {
        this.showError("Failed to load devices");
      }
    } finally {
      this.hideLoading();
    }
  }

  renderDevices() {
    this.deviceList.innerHTML = "";

    if (this.devices.length === 0) {
      this.deviceList.innerHTML =
        '<div class="no-devices">No devices found. Click "Add Device" to get started.</div>';
      return;
    }

    this.devices.forEach((device) => {
      const deviceElement = document.createElement("div");
      deviceElement.className = "device-item";
      deviceElement.dataset.deviceId = device.id;

      const statusClass = device.connected ? "connected" : "disconnected";
      const statusText = device.connected ? "Connected" : "Disconnected";

      deviceElement.innerHTML = `
        <div class="device-info">
          <div class="device-name">${device.name || device.id}</div>
          <div class="device-status ${statusClass}">${statusText}</div>
        </div>
        <div class="device-actions">
          ${
            !device.connected
              ? `<button class="btn btn-icon btn-primary" onclick="app.connectDevice('${device.id}')">Connect</button>`
              : `<button class="btn btn-icon" onclick="app.selectDevice('${device.id}')">Select</button>`
          }
          ${
            device.id !== "default"
              ? `<button class="btn btn-icon btn-danger" onclick="app.removeDevice('${device.id}')">Remove</button>`
              : ""
          }
        </div>
      `;

      this.deviceList.appendChild(deviceElement);
    });
  }

  showAddDeviceModal() {
    this.addDeviceModal.style.display = "flex";
    this.deviceNameInput.value = "";
    this.deviceNameInput.focus();
  }

  hideAddDeviceModal() {
    this.addDeviceModal.style.display = "none";
  }

  async createDevice() {
    const deviceName = this.deviceNameInput.value.trim();
    if (!deviceName) {
      this.showError("Please enter a device name");
      return;
    }

    this.showLoading();
    try {
      const response = await this.apiCall("/devices", {
        method: "POST",
        body: JSON.stringify({
          device_id: deviceName,
        }),
      });

      if (response && response.success) {
        this.hideAddDeviceModal();
        this.showSuccess("Device created successfully");
        await this.loadDevices();
        // Automatically show QR code for the new device
        this.connectDevice(deviceName);
      } else {
        this.showError(
          `Failed to create device: ${response?.message || "Unknown error"}`
        );
      }
    } finally {
      this.hideLoading();
    }
  }

  async connectDevice(deviceId) {
    this.showLoading();
    try {
      const response = await this.apiCall("/devices/qr", {
        method: "POST",
        body: JSON.stringify({
          device_id: deviceId,
        }),
      });

      if (response && response.success) {
        this.showQrModal(response.data.qr_code, deviceId);
        this.startConnectionPolling(deviceId);
      } else {
        this.showError(
          `Failed to generate QR code: ${response?.message || "Unknown error"}`
        );
      }
    } finally {
      this.hideLoading();
    }
  }

  showQrModal(qrCode, deviceId) {
    this.qrModal.style.display = "flex";
    this.qrStatus.textContent = "Scan the QR code with your WhatsApp app";
    this.qrStatus.className = "";

    // Clear previous QR code
    this.qrCodeDisplay.innerHTML = "";

    // Check if QRCode library is available
    if (typeof QRCode !== "undefined") {
      // Generate QR code using QRCode.js library
      QRCode.toCanvas(qrCode, { width: 200, margin: 2 }, (error, canvas) => {
        if (error) {
          console.error("QR Code generation error:", error);
          this.showQrCodeFallback(qrCode);
        } else {
          this.qrCodeDisplay.appendChild(canvas);
        }
      });
    } else {
      // Fallback to external QR code service
      this.showQrCodeFallback(qrCode);
    }
  }

  showQrCodeFallback(qrCode) {
    this.qrCodeDisplay.innerHTML = `
      <img src="https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodeURIComponent(
        qrCode
      )}"
           alt="QR Code"
           style="max-width: 200px; max-height: 200px;" />
    `;
  }

  hideQrModal() {
    this.qrModal.style.display = "none";
    this.qrCodeDisplay.innerHTML = "";
    if (this.connectionPollingInterval) {
      clearInterval(this.connectionPollingInterval);
    }
  }

  startConnectionPolling(deviceId) {
    this.connectionPollingInterval = setInterval(async () => {
      try {
        const response = await this.apiCall(
          `/devices/status?device_id=${deviceId}`
        );
        if (response && response.success) {
          if (response.data.paired && response.data.connected) {
            this.qrStatus.textContent = "Successfully connected!";
            this.qrStatus.className = "success";
            clearInterval(this.connectionPollingInterval);
            setTimeout(() => {
              this.hideQrModal();
              this.loadDevices();
            }, 2000);
          } else if (response.data.paired && !response.data.connected) {
            this.qrStatus.textContent = "Paired! Connecting...";
            this.qrStatus.className = "";
          }
        }
      } catch (error) {
        console.error("Error polling connection status:", error);
      }
    }, 2000);
  }

  async selectDevice(deviceId) {
    // Update UI
    document
      .querySelectorAll(".device-item")
      .forEach((item) => item.classList.remove("active"));
    document
      .querySelector(`[data-device-id="${deviceId}"]`)
      .classList.add("active");

    this.currentDeviceID = deviceId;
    this.chatsSection.style.display = "block";

    // Load chats for this device
    await this.loadChats();
  }

  async removeDevice(deviceId) {
    if (!confirm(`Are you sure you want to remove device "${deviceId}"?`)) {
      return;
    }

    this.showLoading();
    try {
      const response = await this.apiCall("/devices", {
        method: "DELETE",
        body: JSON.stringify({
          device_id: deviceId,
        }),
      });

      if (response && response.success) {
        this.showSuccess("Device removed successfully");
        await this.loadDevices();

        // If the removed device was selected, clear the chat view
        if (this.currentDeviceID === deviceId) {
          this.currentDeviceID = null;
          this.chatsSection.style.display = "none";
          this.messagesContainer.innerHTML =
            '<div class="no-chat-selected"><h3>Welcome to WhatsApp Bridge</h3><p>Select a device to get started</p></div>';
        }
      } else {
        this.showError(
          `Failed to remove device: ${response?.message || "Unknown error"}`
        );
      }
    } finally {
      this.hideLoading();
    }
  }

  async loadChats() {
    if (!this.currentDeviceID) {
      this.showError("Please select a device first");
      return;
    }

    this.showLoading();
    try {
      const response = await this.apiCall(
        `/chats?device_id=${this.currentDeviceID}&limit=50`
      );
      if (response && response.success) {
        this.chats = response.data || [];
        this.renderChats();
      } else {
        this.showError("Failed to load chats");
      }
    } finally {
      this.hideLoading();
    }
  }

  renderChats() {
    this.chatList.innerHTML = "";

    if (this.chats.length === 0) {
      this.chatList.innerHTML = '<div class="no-chats">No chats found</div>';
      return;
    }

    this.chats.forEach((chat) => {
      const chatElement = document.createElement("div");
      chatElement.className = "chat-item";
      chatElement.dataset.jid = chat.jid;

      const lastMessageTime = chat.last_message_time
        ? new Date(chat.last_message_time).toLocaleTimeString([], {
            hour: "2-digit",
            minute: "2-digit",
          })
        : "";

      const lastMessage = chat.last_message || "No messages";
      const displayMessage =
        lastMessage.length > 50
          ? lastMessage.substring(0, 50) + "..."
          : lastMessage;

      chatElement.innerHTML = `
                <div class="chat-time">${lastMessageTime}</div>
                <div class="chat-name">${chat.name || chat.jid}</div>
                <div class="chat-last-message">${displayMessage}</div>
            `;

      chatElement.addEventListener("click", () => this.selectChat(chat));
      this.chatList.appendChild(chatElement);
    });
  }

  filterChats(query) {
    const chatItems = this.chatList.querySelectorAll(".chat-item");
    chatItems.forEach((item) => {
      const chatName = item
        .querySelector(".chat-name")
        .textContent.toLowerCase();
      const chatJID = item.dataset.jid.toLowerCase();
      const matches =
        chatName.includes(query.toLowerCase()) ||
        chatJID.includes(query.toLowerCase());
      item.style.display = matches ? "block" : "none";
    });
  }

  async selectChat(chat) {
    // Update UI
    document
      .querySelectorAll(".chat-item")
      .forEach((item) => item.classList.remove("active"));
    document.querySelector(`[data-jid="${chat.jid}"]`).classList.add("active");

    this.currentChatJID = chat.jid;
    this.chatTitle.textContent = chat.name || chat.jid;
    this.chatJID.textContent = chat.jid;
    this.chatHeader.style.display = "flex";
    this.messageInputContainer.style.display = "block";

    // Load messages for this chat
    await this.loadMessages(chat.jid);
  }

  async loadMessages(chatJID) {
    this.showLoading();
    try {
      const response = await this.apiCall(
        `/messages?chat_jid=${encodeURIComponent(chatJID)}&limit=50`
      );
      if (response && response.success) {
        this.messages = response.data || [];
        this.renderMessages();
      } else {
        this.showError("Failed to load messages");
      }
    } finally {
      this.hideLoading();
    }
  }

  renderMessages() {
    this.messagesContainer.innerHTML = "";

    if (this.messages.length === 0) {
      this.messagesContainer.innerHTML =
        '<div class="no-chat-selected"><p>No messages in this chat</p></div>';
      return;
    }

    // Sort messages by timestamp (oldest first)
    const sortedMessages = [...this.messages].sort(
      (a, b) => new Date(a.timestamp) - new Date(b.timestamp)
    );

    sortedMessages.forEach((message) => {
      const messageElement = document.createElement("div");
      messageElement.className = `message ${
        message.is_from_me ? "from-me" : "from-other"
      }`;

      const messageTime = new Date(message.timestamp).toLocaleTimeString([], {
        hour: "2-digit",
        minute: "2-digit",
      });
      const senderName = message.is_from_me
        ? "You"
        : message.sender || "Unknown";

      let content = message.content || "";
      if (message.media_type) {
        content = `[${message.media_type.toUpperCase()}] ${
          content || message.filename || "Media file"
        }`;
      }

      messageElement.innerHTML = `
                <div class="message-bubble">
                    ${
                      !message.is_from_me
                        ? `<div class="message-sender">${senderName}</div>`
                        : ""
                    }
                    <div class="message-content">${content}</div>
                    <div class="message-time">${messageTime}</div>
                </div>
            `;

      this.messagesContainer.appendChild(messageElement);
    });

    // Scroll to bottom
    this.messagesContainer.scrollTop = this.messagesContainer.scrollHeight;
  }

  async sendMessage() {
    if (!this.currentChatJID || !this.messageText.value.trim()) {
      return;
    }

    const messageContent = this.messageText.value.trim();
    this.messageText.value = "";

    try {
      const response = await this.apiCall("/send", {
        method: "POST",
        body: JSON.stringify({
          recipient: this.currentChatJID,
          message: messageContent,
        }),
      });

      if (response && response.success) {
        this.showSuccess("Message sent successfully");
        // Reload messages to show the new message
        setTimeout(() => this.loadMessages(this.currentChatJID), 1000);
      } else {
        this.showError(
          `Failed to send message: ${response?.message || "Unknown error"}`
        );
      }
    } catch (error) {
      this.showError(`Failed to send message: ${error.message}`);
    }
  }

  showError(message) {
    this.showNotification(message, "error");
  }

  showSuccess(message) {
    this.showNotification(message, "success");
  }

  showNotification(message, type) {
    const notification = document.createElement("div");
    notification.className = type;
    notification.textContent = message;

    // Insert at the top of the messages container
    this.messagesContainer.insertBefore(
      notification,
      this.messagesContainer.firstChild
    );

    // Remove after 3 seconds
    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
    }, 3000);
  }
}

// Initialize the app when the page loads
document.addEventListener("DOMContentLoaded", () => {
  window.app = new WhatsAppBridge();
});
