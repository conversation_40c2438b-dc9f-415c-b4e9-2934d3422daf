class WhatsAppChat {
    constructor() {
        this.apiBase = 'http://localhost:8080/api';
        this.currentChatJID = null;
        this.chats = [];
        this.messages = [];
        
        this.initializeElements();
        this.bindEvents();
        this.loadChats();
    }

    initializeElements() {
        this.chatList = document.getElementById('chat-list');
        this.messagesContainer = document.getElementById('messages-container');
        this.chatHeader = document.getElementById('chat-header');
        this.chatTitle = document.getElementById('chat-title');
        this.chatJID = document.getElementById('chat-jid');
        this.messageInputContainer = document.getElementById('message-input-container');
        this.messageText = document.getElementById('message-text');
        this.sendButton = document.getElementById('send-message');
        this.refreshButton = document.getElementById('refresh-chats');
        this.chatSearch = document.getElementById('chat-search');
        this.loading = document.getElementById('loading');
    }

    bindEvents() {
        this.sendButton.addEventListener('click', () => this.sendMessage());
        this.messageText.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.sendMessage();
            }
        });
        this.refreshButton.addEventListener('click', () => this.loadChats());
        this.chatSearch.addEventListener('input', (e) => this.filterChats(e.target.value));
    }

    showLoading() {
        this.loading.style.display = 'flex';
    }

    hideLoading() {
        this.loading.style.display = 'none';
    }

    async apiCall(endpoint, options = {}) {
        try {
            const response = await fetch(`${this.apiBase}${endpoint}`, {
                headers: {
                    'Content-Type': 'application/json',
                    ...options.headers
                },
                ...options
            });
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            return await response.json();
        } catch (error) {
            console.error('API call failed:', error);
            this.showError(`API call failed: ${error.message}`);
            return null;
        }
    }

    async loadChats() {
        this.showLoading();
        try {
            const response = await this.apiCall('/chats?limit=50');
            if (response && response.success) {
                this.chats = response.data || [];
                this.renderChats();
            } else {
                this.showError('Failed to load chats');
            }
        } finally {
            this.hideLoading();
        }
    }

    renderChats() {
        this.chatList.innerHTML = '';
        
        if (this.chats.length === 0) {
            this.chatList.innerHTML = '<div class="no-chats">No chats found</div>';
            return;
        }

        this.chats.forEach(chat => {
            const chatElement = document.createElement('div');
            chatElement.className = 'chat-item';
            chatElement.dataset.jid = chat.jid;
            
            const lastMessageTime = chat.last_message_time ? 
                new Date(chat.last_message_time).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'}) : '';
            
            const lastMessage = chat.last_message || 'No messages';
            const displayMessage = lastMessage.length > 50 ? lastMessage.substring(0, 50) + '...' : lastMessage;
            
            chatElement.innerHTML = `
                <div class="chat-time">${lastMessageTime}</div>
                <div class="chat-name">${chat.name || chat.jid}</div>
                <div class="chat-last-message">${displayMessage}</div>
            `;
            
            chatElement.addEventListener('click', () => this.selectChat(chat));
            this.chatList.appendChild(chatElement);
        });
    }

    filterChats(query) {
        const chatItems = this.chatList.querySelectorAll('.chat-item');
        chatItems.forEach(item => {
            const chatName = item.querySelector('.chat-name').textContent.toLowerCase();
            const chatJID = item.dataset.jid.toLowerCase();
            const matches = chatName.includes(query.toLowerCase()) || chatJID.includes(query.toLowerCase());
            item.style.display = matches ? 'block' : 'none';
        });
    }

    async selectChat(chat) {
        // Update UI
        document.querySelectorAll('.chat-item').forEach(item => item.classList.remove('active'));
        document.querySelector(`[data-jid="${chat.jid}"]`).classList.add('active');
        
        this.currentChatJID = chat.jid;
        this.chatTitle.textContent = chat.name || chat.jid;
        this.chatJID.textContent = chat.jid;
        this.chatHeader.style.display = 'flex';
        this.messageInputContainer.style.display = 'block';
        
        // Load messages for this chat
        await this.loadMessages(chat.jid);
    }

    async loadMessages(chatJID) {
        this.showLoading();
        try {
            const response = await this.apiCall(`/messages?chat_jid=${encodeURIComponent(chatJID)}&limit=50`);
            if (response && response.success) {
                this.messages = response.data || [];
                this.renderMessages();
            } else {
                this.showError('Failed to load messages');
            }
        } finally {
            this.hideLoading();
        }
    }

    renderMessages() {
        this.messagesContainer.innerHTML = '';
        
        if (this.messages.length === 0) {
            this.messagesContainer.innerHTML = '<div class="no-chat-selected"><p>No messages in this chat</p></div>';
            return;
        }

        // Sort messages by timestamp (oldest first)
        const sortedMessages = [...this.messages].sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp));
        
        sortedMessages.forEach(message => {
            const messageElement = document.createElement('div');
            messageElement.className = `message ${message.is_from_me ? 'from-me' : 'from-other'}`;
            
            const messageTime = new Date(message.timestamp).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
            const senderName = message.is_from_me ? 'You' : (message.sender || 'Unknown');
            
            let content = message.content || '';
            if (message.media_type) {
                content = `[${message.media_type.toUpperCase()}] ${content || message.filename || 'Media file'}`;
            }
            
            messageElement.innerHTML = `
                <div class="message-bubble">
                    ${!message.is_from_me ? `<div class="message-sender">${senderName}</div>` : ''}
                    <div class="message-content">${content}</div>
                    <div class="message-time">${messageTime}</div>
                </div>
            `;
            
            this.messagesContainer.appendChild(messageElement);
        });
        
        // Scroll to bottom
        this.messagesContainer.scrollTop = this.messagesContainer.scrollHeight;
    }

    async sendMessage() {
        if (!this.currentChatJID || !this.messageText.value.trim()) {
            return;
        }

        const messageContent = this.messageText.value.trim();
        this.messageText.value = '';
        
        try {
            const response = await this.apiCall('/send', {
                method: 'POST',
                body: JSON.stringify({
                    recipient: this.currentChatJID,
                    message: messageContent
                })
            });
            
            if (response && response.success) {
                this.showSuccess('Message sent successfully');
                // Reload messages to show the new message
                setTimeout(() => this.loadMessages(this.currentChatJID), 1000);
            } else {
                this.showError(`Failed to send message: ${response?.message || 'Unknown error'}`);
            }
        } catch (error) {
            this.showError(`Failed to send message: ${error.message}`);
        }
    }

    showError(message) {
        this.showNotification(message, 'error');
    }

    showSuccess(message) {
        this.showNotification(message, 'success');
    }

    showNotification(message, type) {
        const notification = document.createElement('div');
        notification.className = type;
        notification.textContent = message;
        
        // Insert at the top of the messages container
        this.messagesContainer.insertBefore(notification, this.messagesContainer.firstChild);
        
        // Remove after 3 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 3000);
    }
}

// Initialize the app when the page loads
document.addEventListener('DOMContentLoaded', () => {
    new WhatsAppChat();
});
