* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background-color: #f0f2f5;
    height: 100vh;
    overflow: hidden;
}

.container {
    display: flex;
    height: 100vh;
}

.sidebar {
    width: 350px;
    background-color: white;
    border-right: 1px solid #e0e0e0;
    display: flex;
    flex-direction: column;
}

.sidebar-header {
    padding: 20px;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.sidebar-header h2 {
    color: #111b21;
    font-size: 20px;
    font-weight: 600;
}

.search-container {
    padding: 15px;
    border-bottom: 1px solid #e0e0e0;
}

.search-container input {
    width: 100%;
    padding: 10px 15px;
    border: 1px solid #e0e0e0;
    border-radius: 20px;
    font-size: 14px;
    outline: none;
}

.search-container input:focus {
    border-color: #00a884;
}

.chat-list {
    flex: 1;
    overflow-y: auto;
}

.chat-item {
    padding: 15px 20px;
    border-bottom: 1px solid #f0f0f0;
    cursor: pointer;
    transition: background-color 0.2s;
}

.chat-item:hover {
    background-color: #f5f6f6;
}

.chat-item.active {
    background-color: #e7f3ff;
}

.chat-name {
    font-weight: 600;
    color: #111b21;
    margin-bottom: 5px;
}

.chat-last-message {
    font-size: 13px;
    color: #667781;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.chat-time {
    font-size: 12px;
    color: #667781;
    float: right;
}

.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    background-color: #efeae2;
}

.chat-header {
    background-color: #f0f2f5;
    padding: 15px 20px;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.chat-header h3 {
    color: #111b21;
    font-size: 18px;
}

.chat-info {
    font-size: 12px;
    color: #667781;
}

.messages-container {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
    background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"><defs><pattern id="pattern" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="%23e0e0e0" opacity="0.3"/></pattern></defs><rect width="100" height="100" fill="url(%23pattern)"/></svg>');
}

.no-chat-selected {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 100%;
    color: #667781;
    text-align: center;
}

.message {
    margin-bottom: 15px;
    display: flex;
    flex-direction: column;
}

.message.from-me {
    align-items: flex-end;
}

.message.from-other {
    align-items: flex-start;
}

.message-bubble {
    max-width: 70%;
    padding: 8px 12px;
    border-radius: 8px;
    word-wrap: break-word;
}

.message.from-me .message-bubble {
    background-color: #d9fdd3;
    color: #111b21;
}

.message.from-other .message-bubble {
    background-color: white;
    color: #111b21;
}

.message-sender {
    font-size: 12px;
    color: #667781;
    margin-bottom: 2px;
    font-weight: 600;
}

.message-time {
    font-size: 11px;
    color: #667781;
    margin-top: 2px;
}

.message-input-container {
    padding: 15px 20px;
    background-color: #f0f2f5;
    border-top: 1px solid #e0e0e0;
}

.message-input {
    display: flex;
    gap: 10px;
    align-items: center;
}

.message-input input {
    flex: 1;
    padding: 10px 15px;
    border: 1px solid #e0e0e0;
    border-radius: 20px;
    font-size: 14px;
    outline: none;
}

.message-input input:focus {
    border-color: #00a884;
}

.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: background-color 0.2s;
}

.btn-primary {
    background-color: #00a884;
    color: white;
}

.btn-primary:hover {
    background-color: #008f72;
}

.btn-small {
    padding: 6px 12px;
    font-size: 12px;
}

.loading {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    color: white;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #00a884;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.error {
    color: #e53e3e;
    background-color: #fed7d7;
    padding: 10px;
    border-radius: 6px;
    margin: 10px 0;
}

.success {
    color: #38a169;
    background-color: #c6f6d5;
    padding: 10px;
    border-radius: 6px;
    margin: 10px 0;
}

.media-message {
    font-style: italic;
    color: #667781;
}

@media (max-width: 768px) {
    .sidebar {
        width: 100%;
        position: absolute;
        z-index: 100;
        height: 100%;
    }
    
    .main-content {
        width: 100%;
    }
    
    .container.chat-open .sidebar {
        display: none;
    }
}
